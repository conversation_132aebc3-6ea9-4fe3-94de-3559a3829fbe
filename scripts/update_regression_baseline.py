#!/Users/<USER>/min-venv/bin/python

"""
Convenient script to update the regression test baseline with a different dataset
"""

import sys
import os

# Add paths
sys.path.append("/Users/<USER>/min-venv-local/lib/python3.9/site-packages")
sys.path.append("/Users/<USER>/treescape")

def update_baseline_with_dataset(dataset_path):
    """Update the regression baseline with a specific dataset"""
    
    # Verify dataset exists
    if not os.path.exists(dataset_path):
        print(f"❌ Error: Dataset path does not exist: {dataset_path}")
        return False
    
    if not os.path.isdir(dataset_path):
        print(f"❌ Error: Dataset path is not a directory: {dataset_path}")
        return False
    
    # Check for .cali files
    import glob
    cali_files = glob.glob(os.path.join(dataset_path, "**", "*.cali"), recursive=True)
    if not cali_files:
        print(f"❌ Error: No .cali files found in {dataset_path}")
        return False
    
    print(f"📁 Found {len(cali_files)} .cali files in {dataset_path}")
    
    # Import and run the regression test in update mode
    from regression_test_performance_CaliReader import run_regression_test
    
    print(f"🔄 Updating baseline with dataset: {dataset_path}")
    success = run_regression_test(update_baseline=True, dataset_path=dataset_path)
    
    if success:
        print("✅ Baseline updated successfully!")
        print("💡 You can now run normal regression tests against this new baseline.")
    else:
        print("❌ Failed to update baseline")
    
    return success

def show_usage():
    """Show usage information"""
    print("Update Regression Baseline Tool")
    print("=" * 40)
    print()
    print("Usage:")
    print("  python scripts/update_regression_baseline.py <dataset_path>")
    print()
    print("Examples:")
    print("  # Update baseline with test_plus_24c dataset")
    print("  python scripts/update_regression_baseline.py /Users/<USER>/datasets/newdemo/test_plus_24c")
    print()
    print("  # Update baseline with test dataset")
    print("  python scripts/update_regression_baseline.py /Users/<USER>/datasets/newdemo/test")
    print()
    print("Available datasets:")
    base_path = "/Users/<USER>/datasets/newdemo/"
    if os.path.exists(base_path):
        for item in os.listdir(base_path):
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                # Count .cali files
                import glob
                cali_count = len(glob.glob(os.path.join(item_path, "**", "*.cali"), recursive=True))
                if cali_count > 0:
                    print(f"  • {item} ({cali_count} .cali files)")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        show_usage()
        sys.exit(1)
    
    dataset_path = sys.argv[1]
    
    # Handle relative paths
    if not os.path.isabs(dataset_path):
        dataset_path = os.path.abspath(dataset_path)
    
    success = update_baseline_with_dataset(dataset_path)
    sys.exit(0 if success else 1)
