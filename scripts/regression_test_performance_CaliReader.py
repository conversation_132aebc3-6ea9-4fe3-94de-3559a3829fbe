#!/Users/<USER>/min-venv/bin/python

"""
Regression Test for performance_CaliReader.py

This script captures the current state of alltests as a baseline and 
detects major differences in future runs.
"""

import sys
import json
import hashlib
import os
from datetime import datetime
from collections import defaultdict, Counter

sys.path.append("/Users/<USER>/min-venv-local/lib/python3.9/site-packages")
sys.path.append("/Users/<USER>/treescape")

import treescape as tr

# Configuration (matching performance_CaliReader.py)
cali_file_loc = "/Users/<USER>/datasets/newdemo/test_plus_80"
xaxis = "launchday"
metadata_key = "test"
processes_for_parallel_read = 15
initial_regions = ["main"]

# Regression test configuration
BASELINE_FILE = "/Users/<USER>/treescape/scripts/regression_baseline_performance_CaliReader.json"
TOLERANCE_PERCENT = 5.0  # Allow 5% variance in numeric values
MAX_MISSING_TESTS = 10   # Allow up to 10 missing tests
MAX_EXTRA_TESTS = 10     # Allow up to 10 extra tests

def extract_test_signature(test):
    """Extract key characteristics of a test for comparison"""
    signature = {}
    
    # Basic metadata
    if hasattr(test, 'metadata'):
        signature['metadata'] = dict(test.metadata)
    
    # Performance tree summary
    if hasattr(test, 'perftree') and isinstance(test.perftree, dict):
        perf_summary = {}
        for node_name, metrics in test.perftree.items():
            if isinstance(metrics, dict):
                node_summary = {}
                for metric_name, value in metrics.items():
                    try:
                        # Convert to float for numeric comparison
                        node_summary[metric_name] = float(value)
                    except (ValueError, TypeError):
                        # Keep as string for non-numeric values
                        node_summary[metric_name] = str(value)
                perf_summary[node_name] = node_summary
        signature['perftree_summary'] = perf_summary
    
    return signature

def generate_dataset_fingerprint(alltests):
    """Generate a comprehensive fingerprint of the dataset"""
    fingerprint = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': len(alltests),
        'test_signatures': [],
        'metadata_summary': {},
        'perftree_summary': {},
        'data_hash': None
    }
    
    # Extract signatures for each test
    for i, test in enumerate(alltests):
        signature = extract_test_signature(test)
        signature['test_index'] = i
        fingerprint['test_signatures'].append(signature)
    
    # Metadata summary
    metadata_counts = defaultdict(Counter)
    for test in alltests:
        if hasattr(test, 'metadata'):
            for key, value in test.metadata.items():
                metadata_counts[key][str(value)] += 1
    
    fingerprint['metadata_summary'] = {
        key: dict(counter) for key, counter in metadata_counts.items()
    }
    
    # Performance tree summary
    node_metrics = defaultdict(lambda: defaultdict(list))
    for test in alltests:
        if hasattr(test, 'perftree') and isinstance(test.perftree, dict):
            for node_name, metrics in test.perftree.items():
                if isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        try:
                            node_metrics[node_name][metric_name].append(float(value))
                        except (ValueError, TypeError):
                            pass
    
    # Calculate statistics for numeric metrics
    perf_stats = {}
    for node_name, metrics in node_metrics.items():
        node_stats = {}
        for metric_name, values in metrics.items():
            if values:
                node_stats[metric_name] = {
                    'count': len(values),
                    'min': min(values),
                    'max': max(values),
                    'avg': sum(values) / len(values),
                    'sum': sum(values)
                }
        perf_stats[node_name] = node_stats
    
    fingerprint['perftree_summary'] = perf_stats
    
    # Generate hash of the entire dataset for quick comparison
    dataset_str = json.dumps(fingerprint['test_signatures'], sort_keys=True)
    fingerprint['data_hash'] = hashlib.md5(dataset_str.encode()).hexdigest()
    
    return fingerprint

def save_baseline(fingerprint):
    """Save the current fingerprint as the baseline"""
    with open(BASELINE_FILE, 'w') as f:
        json.dump(fingerprint, f, indent=2)
    print(f"✅ Baseline saved to {BASELINE_FILE}")

def load_baseline():
    """Load the baseline fingerprint"""
    if not os.path.exists(BASELINE_FILE):
        return None
    
    with open(BASELINE_FILE, 'r') as f:
        return json.load(f)

def compare_fingerprints(baseline, current):
    """Compare current fingerprint against baseline"""
    issues = []
    warnings = []
    
    # Check total test count
    baseline_count = baseline['total_tests']
    current_count = current['total_tests']
    
    if abs(current_count - baseline_count) > MAX_MISSING_TESTS:
        issues.append(f"Test count changed significantly: {baseline_count} -> {current_count}")
    elif current_count != baseline_count:
        warnings.append(f"Test count changed: {baseline_count} -> {current_count}")
    
    # Check data hash for quick comparison
    if baseline['data_hash'] != current['data_hash']:
        warnings.append("Data hash changed - detailed comparison needed")
        
        # Compare performance tree statistics
        baseline_perf = baseline.get('perftree_summary', {})
        current_perf = current.get('perftree_summary', {})
        
        for node_name in set(baseline_perf.keys()) | set(current_perf.keys()):
            if node_name not in baseline_perf:
                warnings.append(f"New performance node: {node_name}")
                continue
            if node_name not in current_perf:
                issues.append(f"Missing performance node: {node_name}")
                continue
            
            baseline_node = baseline_perf[node_name]
            current_node = current_perf[node_name]
            
            for metric_name in set(baseline_node.keys()) | set(current_node.keys()):
                if metric_name not in baseline_node:
                    warnings.append(f"New metric {node_name}.{metric_name}")
                    continue
                if metric_name not in current_node:
                    issues.append(f"Missing metric {node_name}.{metric_name}")
                    continue
                
                baseline_stats = baseline_node[metric_name]
                current_stats = current_node[metric_name]
                
                # Compare key statistics
                for stat_name in ['count', 'min', 'max', 'avg', 'sum']:
                    if stat_name in baseline_stats and stat_name in current_stats:
                        baseline_val = baseline_stats[stat_name]
                        current_val = current_stats[stat_name]
                        
                        if baseline_val != 0:
                            percent_change = abs(current_val - baseline_val) / abs(baseline_val) * 100
                            if percent_change > TOLERANCE_PERCENT:
                                issues.append(
                                    f"Significant change in {node_name}.{metric_name}.{stat_name}: "
                                    f"{baseline_val:.3f} -> {current_val:.3f} ({percent_change:.1f}% change)"
                                )
    
    return issues, warnings

def run_regression_test(update_baseline=False, dataset_path=None):
    """Run the regression test"""

    # Use provided dataset path or default
    test_dataset = dataset_path if dataset_path else cali_file_loc

    print("=" * 60)
    print("REGRESSION TEST: performance_CaliReader.py")
    print("=" * 60)
    print(f"Dataset: {test_dataset}")

    if update_baseline:
        print("🔄 UPDATE BASELINE MODE")
    else:
        print("🧪 COMPARISON MODE")
    print("=" * 60)

    # Load data
    print("Loading data...")

    if __name__ == '__main__':
        from multiprocessing import freeze_support
        freeze_support()

        inclusive_strs = ["min#inclusive#sum#time.duration",
                          "max#inclusive#sum#time.duration",
                          "avg#inclusive#sum#time.duration",
                          "sum#inclusive#sum#time.duration"]

        caliReader = tr.CaliReader(test_dataset, processes_for_parallel_read, inclusive_strs)
        tsm = tr.TreeScapeModel(caliReader)
        alltests = sorted(tsm, key=lambda x: x.metadata[xaxis])

        print(f"Loaded {len(alltests)} tests")

        # Generate current fingerprint
        print("Generating dataset fingerprint...")
        current_fingerprint = generate_dataset_fingerprint(alltests)
        current_fingerprint['dataset_path'] = test_dataset  # Track which dataset was used

        if update_baseline:
            # Update baseline mode
            print("📝 Updating baseline...")
            save_baseline(current_fingerprint)
            print("✅ Baseline updated successfully!")
            print(f"📊 New baseline: {len(alltests)} tests from {test_dataset}")
            return True

        # Normal comparison mode
        baseline_fingerprint = load_baseline()

        if baseline_fingerprint is None:
            print("📝 No baseline found. Creating baseline...")
            save_baseline(current_fingerprint)
            print("✅ Baseline created successfully!")
            return True

        # Show baseline info
        baseline_dataset = baseline_fingerprint.get('dataset_path', 'Unknown')
        baseline_count = baseline_fingerprint.get('total_tests', 'Unknown')
        print(f"📋 Baseline: {baseline_count} tests from {baseline_dataset}")

        # Compare against baseline
        print("Comparing against baseline...")
        issues, warnings = compare_fingerprints(baseline_fingerprint, current_fingerprint)

        # Report results
        print("\n" + "=" * 60)
        print("REGRESSION TEST RESULTS")
        print("=" * 60)

        if not issues and not warnings:
            print("✅ PASS: No significant changes detected")
            return True

        if warnings:
            print(f"⚠️  WARNINGS ({len(warnings)}):")
            for warning in warnings:
                print(f"   • {warning}")
            print()

        if issues:
            print(f"❌ ISSUES ({len(issues)}):")
            for issue in issues:
                print(f"   • {issue}")
            print()
            print("❌ FAIL: Significant changes detected!")
            return False
        else:
            print("✅ PASS: Only minor changes detected")
            return True

if __name__ == '__main__':
    success = run_regression_test()
    sys.exit(0 if success else 1)
