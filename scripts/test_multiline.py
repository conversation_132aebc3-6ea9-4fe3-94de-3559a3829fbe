
cali_file_loc = "/Users/<USER>/datasets/newdemo/test_plus_24a"
#cali_file_loc = "/Users/<USER>/datasets/newdemo/test_plus_48"

#cali_file_loc = "/Users/<USER>/datasets/newdemo/test"
xaxis = "launchday"
metadata_key = "test"
processes_for_parallel_read = 15
initial_regions = ["main"]

import sys
import warnings

# Filter out the specific Roundtrip warning
warnings.filterwarnings("ignore", message=".*Roundtrip module could not be loaded.*")

sys.path.append("/Users/<USER>/min-venv-local/lib/python3.9/site-packages")
sys.path.append("/Users/<USER>/treescape")

import treescape as tr

if __name__ == '__main__':
    from multiprocessing import freeze_support
    freeze_support()

    inclusive_strs = ["min#inclusive#sum#time.duration",
                                      "max#inclusive#sum#time.duration",
                                      "avg#inclusive#sum#time.duration",
                                      "sum#inclusive#sum#time.duration"]
    caliReader = tr.CaliReader( cali_file_loc, processes_for_parallel_read, inclusive_strs )
    tsm = tr.TreeScapeModel( caliReader)
    #Always be sure to sort your data into some reasonable way.
    alltests = sorted(tsm, key=lambda x: x.metadata[xaxis])

    grapher = tr.MultiLine(alltests)
    for region in initial_regions:
        grapher.plot_sums( xaxis, region, metadata_key )