[{"min#inclusive#sum#time.duration": 0.004547, "max#inclusive#sum#time.duration": 0.004547, "avg#inclusive#sum#time.duration": 0.004547}, {"function": "main", "min#inclusive#sum#time.duration": 79.950869, "max#inclusive#sum#time.duration": 79.950869, "avg#inclusive#sum#time.duration": 79.950869}, {"loop": "lulesh.cycle", "function": "main", "min#inclusive#sum#time.duration": 79.885921, "max#inclusive#sum#time.duration": 79.885921, "avg#inclusive#sum#time.duration": 79.885921}, {"loop": "lulesh.cycle", "function": "main/TimeIncrement", "min#inclusive#sum#time.duration": 0.032186, "max#inclusive#sum#time.duration": 0.032186, "avg#inclusive#sum#time.duration": 0.032186}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog", "min#inclusive#sum#time.duration": 79.840035, "max#inclusive#sum#time.duration": 79.840035, "avg#inclusive#sum#time.duration": 79.840035}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeNodal", "min#inclusive#sum#time.duration": 57.258493, "max#inclusive#sum#time.duration": 57.258493, "avg#inclusive#sum#time.duration": 57.258493}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeNodal/CalcForceForNodes", "min#inclusive#sum#time.duration": 56.424499, "max#inclusive#sum#time.duration": 56.424499, "avg#inclusive#sum#time.duration": 56.424499}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeNodal/CalcForceForNodes/CalcVolumeForceForElems", "min#inclusive#sum#time.duration": 56.272907, "max#inclusive#sum#time.duration": 56.272907, "avg#inclusive#sum#time.duration": 56.272907}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeNodal/CalcForceForNodes/CalcVolumeForceForElems/IntegrateStressForElems", "min#inclusive#sum#time.duration": 11.209784, "max#inclusive#sum#time.duration": 11.209784, "avg#inclusive#sum#time.duration": 11.209784}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeNodal/CalcForceForNodes/CalcVolumeForceForElems/CalcHourglassControlForElems", "min#inclusive#sum#time.duration": 44.65023, "max#inclusive#sum#time.duration": 44.65023, "avg#inclusive#sum#time.duration": 44.65023}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeNodal/CalcForceForNodes/CalcVolumeForceForElems/CalcHourglassControlForElems/CalcFBHourglassForceForElems", "min#inclusive#sum#time.duration": 14.379508, "max#inclusive#sum#time.duration": 14.379508, "avg#inclusive#sum#time.duration": 14.379508}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements", "min#inclusive#sum#time.duration": 21.69578, "max#inclusive#sum#time.duration": 21.69578, "avg#inclusive#sum#time.duration": 21.69578}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements/CalcLagrangeElements", "min#inclusive#sum#time.duration": 2.394917, "max#inclusive#sum#time.duration": 2.394917, "avg#inclusive#sum#time.duration": 2.394917}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements/CalcLagrangeElements/CalcKinematicsForElems", "min#inclusive#sum#time.duration": 2.260439, "max#inclusive#sum#time.duration": 2.260439, "avg#inclusive#sum#time.duration": 2.260439}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements/CalcQForElems", "min#inclusive#sum#time.duration": 2.870288, "max#inclusive#sum#time.duration": 2.870288, "avg#inclusive#sum#time.duration": 2.870288}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements/CalcQForElems/CalcMonotonicQForElems", "min#inclusive#sum#time.duration": 1.389369, "max#inclusive#sum#time.duration": 1.389369, "avg#inclusive#sum#time.duration": 1.389369}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements/ApplyMaterialPropertiesForElems", "min#inclusive#sum#time.duration": 16.367402, "max#inclusive#sum#time.duration": 16.367402, "avg#inclusive#sum#time.duration": 16.367402}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements/ApplyMaterialPropertiesForElems/EvalEOSForElems", "min#inclusive#sum#time.duration": 16.247558, "max#inclusive#sum#time.duration": 16.247558, "avg#inclusive#sum#time.duration": 16.247558}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/LagrangeElements/ApplyMaterialPropertiesForElems/EvalEOSForElems/CalcEnergyForElems", "min#inclusive#sum#time.duration": 12.419295, "max#inclusive#sum#time.duration": 12.419295, "avg#inclusive#sum#time.duration": 12.419295}, {"loop": "lulesh.cycle", "function": "main/LagrangeLeapFrog/CalcTimeConstraintsForElems", "min#inclusive#sum#time.duration": 0.871165, "max#inclusive#sum#time.duration": 0.871165, "avg#inclusive#sum#time.duration": 0.871165}]