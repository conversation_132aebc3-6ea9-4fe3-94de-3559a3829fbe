from time import time
from pprint import pprint

class MyTimer:

    markers = []

    def __init__(self, name):

        MyTimer.start = 1 
        if name == "start":
            self.startNow()
        else:
            self.mark( name )


    def startNow(self):
        MyTimer.start = time()
        MyTimer.markers = []

    def mark(self, label_name):

        now = time()

        if not MyTimer.markers:
            last_time = MyTimer.start
        else:
            last_time = MyTimer.markers[-1][2]

        elapsed = now - last_time
        MyTimer.markers.append([label_name, elapsed, now])

    def print(self):

        elap = time() - MyTimer.start
        MyTimer.markers.append(["Total Elapsed time", elap, 0])

        for label, elapsed, value in MyTimer.markers:

            padded_label = label.ljust(40)
            print(f"{padded_label}        {elapsed}")


