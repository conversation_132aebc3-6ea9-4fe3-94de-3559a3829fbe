#matplotlib.use("TkAgg")
import matplotlib
matplotlib.use('module://matplotlib_inline.backend_inline')  # for Jupyter notebook

import matplotlib.pyplot as plt

from collections import defaultdict
from datetime import datetime

# Example:
# tsm3 = TreeScapeModel( caliReader )
#
# alltests = sorted(tsm3, key=lambda x: x.metadata["launchday"])
#
# grapher = MultiLine(alltests)
# grapher.plot_sums( "launchday", "main" )
# grapher.plot_sums( "figure_of_merit", "main" )

class MultiLine:
    def __init__(self, all_tests):
        self.all_tests = all_tests

    def plot_sums(self, xaxis, node_name, line_metadata_name ):
        # Create a figure

        self.plot_one_sum( self.all_tests, xaxis, node_name, line_metadata_name )
        #[t for t in self.all_tests if t.metadata["test"] == testname])


    def launchday_to_date(self, epoch_timestamp):
        # Ensure the timestamp is an integer
        epoch_timestamp = int(epoch_timestamp)

        # Create a datetime object from the epoch timestamp
        date = datetime.fromtimestamp(epoch_timestamp)

        # Define month abbreviations
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

        # Extract date components
        year = date.year
        month = months[date.month - 1]  # Months are 1-based in Python
        day = f"{date.day:02d}"
        hours = f"{date.hour:02d}"
        minutes = f"{date.minute:02d}"
        seconds = f"{date.second:02d}"

        # Create the readable string
        readable_string = f"{year}-{month}-{day} {hours}:{minutes}:{seconds}"

        return readable_string


    def plot_one_sum(self, tests, xaxis, node_name, line_metadata_name):

        plt.figure(figsize=(12, 6))

        test_data = defaultdict(lambda: ([], []))  # Dictionary to store labels and sums per test type

        # Loop through each test in tests
        for test in tests:
            # going through all the Runs
            #print(test)
            #exit()
            if hasattr(test, 'perftree') and isinstance(test.perftree, dict):
                myx = test.metadata[xaxis]
                #print(f"{metavar} for test {test.metadata.get('test', 'Unknown')}: {myx}")  # Debug line
                myx = self.convert_to_number(myx)
                test_name = test.metadata.get(line_metadata_name, "Unknown")  # Use 'Unknown' if metadata is missing

                for key, metrics in test.perftree.items():
                    if key == node_name and 'sum' in metrics:

                        if xaxis == "launchday" or xaxis == "launchdate":
                            myx = self.launchday_to_date( myx )

                        test_data[test_name][0].append(myx)  # Store job size (x-axis)
                        test_data[test_name][1].append(float(metrics['sum']))  # Store sum value (y-axis)

        # Plot each unique test type separately
        for test_name, (labels, sums) in test_data.items():
            labels, sums = self.make_x_uniq(labels, sums)
            print(labels, sums)
            plt.plot(labels, sums, label=f'{test_name}')

        # Configure plot
        plt.title('Sum Values from Node: ' + node_name)
        plt.xlabel(xaxis)
        plt.ylabel('Sum Values')
        plt.xticks(rotation=45, ha='right')
        plt.legend()
        plt.tight_layout()

        # Show the plot
        plt.show()

    def make_x_uniq(self, a, b):
        import numpy as np

        # Create a dictionary to store the sum of corresponding b values for each unique a
        unique_a = sorted(set(a))  # Get unique values of a, sorted ascending
        summed_b = []

        # Sum the corresponding b values for each unique value in a
        for value in unique_a:
            summed_b.append(sum(b[i] for i in range(len(a)) if a[i] == value))

        # Convert the results into arrays (if needed)
        a = np.array(unique_a)
        b = np.array(summed_b)

        return a, b

    def convert_to_number(self, s):
        try:
            # Try converting to an integer
            return int(s)
        except ValueError:
            return float(s)
