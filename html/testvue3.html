<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue 3 Incrementer Example</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@3.2.20"></script>
</head>
<body>
  <div id="mainApp">
    <button @click="increment">Increment</button>
    <p>Count2: {{ count }}</p>
  </div>

  <script>
    const { createApp, ref } = Vue;

    const app = createApp({
      setup() {
        const count = ref(0);

        const increment = () => {
          count.value++;
        };

        return {
          count,
          increment,
        };
      },
    });

    app.mount('#mainApp');
  </script>
</body>
</html>
