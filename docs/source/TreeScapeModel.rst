TreeScapeModel
=============================
The TreeScape Model lets you define what you want to see in your charts.  You can
specify x axis and annotations.

.. automodule:: TreeScapeModel
   :members:
   :undoc-members:
   :show-inheritance:



This is an example of how to set visible annotations.  The annotations given are from the lulesh gen data set.
.. code-block:: python

   model.setVisibleAnnotations(['CalcLagrangeElements', 'CalcQForElems', 'ApplyMaterialPropertiesForElems'])


