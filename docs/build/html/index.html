<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Welcome to TreeScape’s documentation! &mdash; TreeScape  documentation</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/doctools.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="TreeScapeModel" href="TreeScapeModel.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="#" class="icon icon-home"> TreeScape
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="TreeScapeModel.html">TreeScapeModel</a></li>
<li class="toctree-l1"><a class="reference internal" href="StackedLine.html">StackedLine Viz</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">TreeScape</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home"></a></li>
      <li class="breadcrumb-item active">Welcome to TreeScape’s documentation!</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="welcome-to-treescape-s-documentation">
<h1>Welcome to TreeScape’s documentation!<a class="headerlink" href="#welcome-to-treescape-s-documentation" title="Permalink to this headline"></a></h1>
<div class="toctree-wrapper compound">
</div>
<p>TreeScape shows you line charts and flamegraphs of the data you provide.</p>
<section id="example-inputs">
<h2>Example Inputs<a class="headerlink" href="#example-inputs" title="Permalink to this headline"></a></h2>
<p>You can select runs by placing them in a directory and creating a th_ens and profiles object.
This is an example of how to build a th_ens object.   The th_ens object is then
used to populate the TreeScapeModel with runs.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">import</span> <span class="nn">platform</span>
<span class="kn">import</span> <span class="nn">datetime</span> <span class="k">as</span> <span class="nn">dt</span>

<span class="n">input_deploy_dir_str</span> <span class="o">=</span> <span class="s2">&quot;/usr/gapps/spot/dev/&quot;</span>
<span class="n">machine</span> <span class="o">=</span> <span class="n">platform</span><span class="o">.</span><span class="n">uname</span><span class="p">()</span><span class="o">.</span><span class="n">machine</span>

<span class="n">sys</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;/Users/<USER>/thicket&quot;</span><span class="p">)</span>

<span class="kn">import</span> <span class="nn">re</span>

<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
<span class="kn">import</span> <span class="nn">pandas</span> <span class="k">as</span> <span class="nn">pd</span>
<span class="kn">import</span> <span class="nn">matplotlib.pyplot</span> <span class="k">as</span> <span class="nn">plt</span>
<span class="kn">from</span> <span class="nn">IPython.display</span> <span class="kn">import</span> <span class="n">display</span>
<span class="kn">from</span> <span class="nn">IPython.display</span> <span class="kn">import</span> <span class="n">HTML</span>

<span class="kn">import</span> <span class="nn">hatchet</span> <span class="k">as</span> <span class="nn">ht</span>
<span class="kn">import</span> <span class="nn">thicket</span> <span class="k">as</span> <span class="nn">tt</span>

<span class="c1"># get a list of all cali files in subdirectory - recursively</span>
<span class="kn">from</span> <span class="nn">glob</span> <span class="kn">import</span> <span class="n">glob</span>
<span class="kn">import</span> <span class="nn">os</span>

<span class="n">PATH</span> <span class="o">=</span> <span class="s1">&#39;/Users/<USER>/lulesh_gen/100/&#39;</span>
<span class="n">profiles</span> <span class="o">=</span> <span class="p">[</span><span class="n">y</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">os</span><span class="o">.</span><span class="n">walk</span><span class="p">(</span><span class="n">PATH</span><span class="p">)</span> <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="n">glob</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">x</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="s1">&#39;*.cali&#39;</span><span class="p">))]</span>

<span class="c1">#  this contains some metadata we need.</span>
<span class="c1">#  also contains the tree data.</span>
<span class="n">th_ens</span> <span class="o">=</span> <span class="n">tt</span><span class="o">.</span><span class="n">Thicket</span><span class="o">.</span><span class="n">from_caliperreader</span><span class="p">(</span><span class="n">profiles</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="profiles">
<h2>Profiles<a class="headerlink" href="#profiles" title="Permalink to this headline"></a></h2>
<p>Profiles is a list of *.cali input files thicket requires for the caliperreader.
It looks like this:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">example</span> <span class="o">=</span> <span class="p">[</span>
   <span class="s1">&#39;/Users/<USER>/lulesh_gen/100/49.cali&#39;</span><span class="p">,</span>
   <span class="s1">&#39;/Users/<USER>/lulesh_gen/100/73.cali&#39;</span><span class="p">,</span>
   <span class="s1">&#39;/Users/<USER>/lulesh_gen/100/24.cali&#39;</span><span class="p">,</span>
   <span class="s1">&#39;/Users/<USER>/lulesh_gen/100/32.cali&#39;</span>
   <span class="p">]</span>
</pre></div>
</div>
</section>
<section id="usage">
<h2>Usage<a class="headerlink" href="#usage" title="Permalink to this headline"></a></h2>
<p>In a Jupyter notebook use the following python code.  Above, you created two variables needed by graph renderer
profiles and th_ens.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span>

<span class="n">sys</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;python&quot;</span><span class="p">)</span>
<span class="n">sys</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;viz&quot;</span><span class="p">)</span>

<span class="kn">from</span> <span class="nn">StackedLine</span> <span class="kn">import</span> <span class="n">StackedLine</span>
<span class="kn">from</span> <span class="nn">ThicketWrapper</span> <span class="kn">import</span> <span class="n">ThicketWrapper</span><span class="p">,</span> <span class="n">get_th_ens</span>
<span class="kn">from</span> <span class="nn">TreeScapeModel</span> <span class="kn">import</span> <span class="n">TreeScapeModel</span>

<span class="n">th_ens</span><span class="p">,</span> <span class="n">profiles</span> <span class="o">=</span> <span class="n">get_th_ens</span><span class="p">()</span>

<span class="n">model</span> <span class="o">=</span> <span class="n">TreeScapeModel</span><span class="p">(</span><span class="n">th_ens</span><span class="p">,</span> <span class="n">profiles</span><span class="p">)</span>
<span class="n">model</span><span class="o">.</span><span class="n">setVisibleAnnotations</span><span class="p">([</span><span class="s1">&#39;main&#39;</span><span class="p">])</span>
<span class="n">model</span><span class="o">.</span><span class="n">setXAxis</span><span class="p">(</span><span class="s2">&quot;launchdate&quot;</span><span class="p">)</span>

<span class="n">sl</span> <span class="o">=</span> <span class="n">StackedLine</span><span class="p">()</span>
<span class="n">sl</span><span class="o">.</span><span class="n">render</span><span class="p">(</span><span class="s2">&quot;Line&quot;</span><span class="p">,</span> <span class="n">model</span> <span class="p">)</span>
</pre></div>
</div>
</section>
<section id="tree">
<h2>Tree<a class="headerlink" href="#tree" title="Permalink to this headline"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="TreeScapeModel.html">TreeScapeModel</a></li>
<li class="toctree-l1"><a class="reference internal" href="StackedLine.html">StackedLine Viz</a></li>
</ul>
</div>
</section>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Permalink to this headline"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
</ul>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="TreeScapeModel.html" class="btn btn-neutral float-right" title="TreeScapeModel" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Pascal.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>