<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TreeScapeModel &mdash; TreeScape  documentation</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/doctools.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="StackedLine Viz" href="StackedLine.html" />
    <link rel="prev" title="Welcome to TreeScape’s documentation!" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="index.html" class="icon icon-home"> TreeScape
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">TreeScapeModel</a></li>
<li class="toctree-l1"><a class="reference internal" href="StackedLine.html">StackedLine Viz</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">TreeScape</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home"></a></li>
      <li class="breadcrumb-item active">TreeScapeModel</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/TreeScapeModel.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="treescapemodel">
<h1>TreeScapeModel<a class="headerlink" href="#treescapemodel" title="Permalink to this headline"></a></h1>
<p>The TreeScape Model lets you define what you want to see in your charts.  You can
specify x axis and annotations.</p>
<span class="target" id="module-TreeScapeModel"></span><dl class="py class">
<dt class="sig sig-object py" id="TreeScapeModel.TreeScapeModel">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">TreeScapeModel.</span></span><span class="sig-name descname"><span class="pre">TreeScapeModel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">th_ens</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">profiles</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#TreeScapeModel.TreeScapeModel" title="Permalink to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="TreeScapeModel.TreeScapeModel.get_PJ_bus">
<span class="sig-name descname"><span class="pre">get_PJ_bus</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#TreeScapeModel.TreeScapeModel.get_PJ_bus" title="Permalink to this definition"></a></dt>
<dd><p>For internal use.  The PJ bus allows Python variables to be sent down to the
javascript layer.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns</dt>
<dd class="field-odd"><p>An object containing the things required for plotting.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="TreeScapeModel.TreeScapeModel.setGroupBy">
<span class="sig-name descname"><span class="pre">setGroupBy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">group_by</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#TreeScapeModel.TreeScapeModel.setGroupBy" title="Permalink to this definition"></a></dt>
<dd><p>You can set the group by of the stacked line chart you want to see.  This will create multiple
charts.  Each chart use value of the group by parameter.  For example, if jobsize is selected,
the first plot will only show jobsize = 4, the second plot might use jobsize = 13, etc.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>xaxis_name</strong> – examples are jobsize, problem_size, launchdate, etc.</p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="TreeScapeModel.TreeScapeModel.setVisibleAnnotations">
<span class="sig-name descname"><span class="pre">setVisibleAnnotations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nameOfLinesToPlot</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#TreeScapeModel.TreeScapeModel.setVisibleAnnotations" title="Permalink to this definition"></a></dt>
<dd><p>You can set the names of the lines you wish to plot</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>nameOfLinesToPlot</strong> – provide an array of annotation (str) you wish to plot</p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="TreeScapeModel.TreeScapeModel.setXAggregation">
<span class="sig-name descname"><span class="pre">setXAggregation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">aggregation</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#TreeScapeModel.TreeScapeModel.setXAggregation" title="Permalink to this definition"></a></dt>
<dd><p>This determines how multiple values are aggregated</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>aggregation</strong> – examples are sum, max, min, average.</p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="TreeScapeModel.TreeScapeModel.setXAxis">
<span class="sig-name descname"><span class="pre">setXAxis</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">xaxis_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#TreeScapeModel.TreeScapeModel.setXAxis" title="Permalink to this definition"></a></dt>
<dd><p>You can set the X axis of the stacked line chart you want to see.  The name given
must be present as an attribute in the meta dataset.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>xaxis_name</strong> – examples are jobsize, problem_size, launchdate, etc.</p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="TreeScapeModel.TreeScapeModel.setYAxis">
<span class="sig-name descname"><span class="pre">setYAxis</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">yaxis_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#TreeScapeModel.TreeScapeModel.setYAxis" title="Permalink to this definition"></a></dt>
<dd><p>You can set the Y axis of the stacked line chart you want to see.  the name given
must be present as an attribute in the meta dataset.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters</dt>
<dd class="field-odd"><p><strong>yaxis_name</strong> – </p>
</dd>
<dt class="field-even">Returns</dt>
<dd class="field-even"><p></p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<p>This is an example of how to set visible annotations.  The annotations given are from the lulesh gen data set.
.. code-block:: python</p>
<blockquote>
<div><p>model.setVisibleAnnotations([‘CalcLagrangeElements’, ‘CalcQForElems’, ‘ApplyMaterialPropertiesForElems’])</p>
</div></blockquote>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Welcome to TreeScape’s documentation!" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="StackedLine.html" class="btn btn-neutral float-right" title="StackedLine Viz" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Pascal.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>