<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Index &mdash; TreeScape  documentation</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/doctools.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="index.html" class="icon icon-home"> TreeScape
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <ul>
<li class="toctree-l1"><a class="reference internal" href="TreeScapeModel.html">TreeScapeModel</a></li>
<li class="toctree-l1"><a class="reference internal" href="StackedLine.html">StackedLine Viz</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">TreeScape</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home"></a></li>
      <li class="breadcrumb-item active">Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#G"><strong>G</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 
</div>
<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="TreeScapeModel.html#TreeScapeModel.TreeScapeModel.get_PJ_bus">get_PJ_bus() (TreeScapeModel.TreeScapeModel method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    module

      <ul>
        <li><a href="StackedLine.html#module-StackedLine">StackedLine</a>
</li>
        <li><a href="TreeScapeModel.html#module-TreeScapeModel">TreeScapeModel</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="StackedLine.html#StackedLine.StackedLine.render">render() (StackedLine.StackedLine method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="StackedLine.html#StackedLine.StackedLine.renderFlamegraph">renderFlamegraph() (StackedLine.StackedLine method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="TreeScapeModel.html#TreeScapeModel.TreeScapeModel.setGroupBy">setGroupBy() (TreeScapeModel.TreeScapeModel method)</a>
</li>
      <li><a href="TreeScapeModel.html#TreeScapeModel.TreeScapeModel.setVisibleAnnotations">setVisibleAnnotations() (TreeScapeModel.TreeScapeModel method)</a>
</li>
      <li><a href="TreeScapeModel.html#TreeScapeModel.TreeScapeModel.setXAggregation">setXAggregation() (TreeScapeModel.TreeScapeModel method)</a>
</li>
      <li><a href="TreeScapeModel.html#TreeScapeModel.TreeScapeModel.setXAxis">setXAxis() (TreeScapeModel.TreeScapeModel method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="TreeScapeModel.html#TreeScapeModel.TreeScapeModel.setYAxis">setYAxis() (TreeScapeModel.TreeScapeModel method)</a>
</li>
      <li>
    StackedLine

      <ul>
        <li><a href="StackedLine.html#module-StackedLine">module</a>
</li>
      </ul></li>
      <li><a href="StackedLine.html#StackedLine.StackedLine">StackedLine (class in StackedLine)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    TreeScapeModel

      <ul>
        <li><a href="TreeScapeModel.html#module-TreeScapeModel">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="TreeScapeModel.html#TreeScapeModel.TreeScapeModel">TreeScapeModel (class in TreeScapeModel)</a>
</li>
  </ul></td>
</tr></table>



           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2024, Pascal.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>