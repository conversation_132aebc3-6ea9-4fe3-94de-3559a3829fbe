Search.setIndex({docnames:["StackedLine","TreeScapeModel","index"],envversion:{"sphinx.domains.c":2,"sphinx.domains.changeset":1,"sphinx.domains.citation":1,"sphinx.domains.cpp":5,"sphinx.domains.index":1,"sphinx.domains.javascript":2,"sphinx.domains.math":2,"sphinx.domains.python":3,"sphinx.domains.rst":2,"sphinx.domains.std":2,sphinx:56},filenames:["StackedLine.rst","TreeScapeModel.rst","index.rst"],objects:{"":[[0,0,0,"-","StackedLine"],[1,0,0,"-","TreeScapeModel"]],"StackedLine.StackedLine":[[0,2,1,"","render"],[0,2,1,"","renderFlamegraph"]],"TreeScapeModel.TreeScapeModel":[[1,2,1,"","get_PJ_bus"],[1,2,1,"","setGroupBy"],[1,2,1,"","setVisibleAnnotations"],[1,2,1,"","setXAggregation"],[1,2,1,"","setXAxis"],[1,2,1,"","setYAxis"]],StackedLine:[[0,1,1,"","StackedLine"]],TreeScapeModel:[[1,1,1,"","TreeScapeModel"]]},objnames:{"0":["py","module","Python module"],"1":["py","class","Python class"],"2":["py","method","Python method"]},objtypes:{"0":"py:module","1":"py:class","2":"py:method"},terms:{"0":2,"100":2,"13":1,"2":[],"24":2,"32":2,"4":1,"49":2,"73":2,"class":[0,1],"import":2,"return":[0,1],For:1,In:2,It:2,The:[1,2],abov:2,add:[],aggreg:1,all:2,allow:1,also:2,an:[1,2],annot:1,append:2,applymaterialpropertiesforelem:1,ar:1,arrai:1,aschwanden1:2,attribut:1,averag:1,axi:1,base:[0,1],block:1,bu:1,build:2,calclagrangeel:1,calcqforelem:1,cali:2,caliperread:2,can:[1,2],chart:[0,1,2],charttyp:0,code:[1,2],combo:0,contain:[1,2],creat:[1,2],data:[0,1,2],dataset:1,datetim:2,defin:1,describ:0,determin:1,dev:2,directori:2,displai:2,down:1,dt:2,dump:[],each:1,elapsed_tim:[],etc:1,exampl:1,file:2,first:1,flamegraph:[0,2],follow:2,from:[1,2],from_caliperread:2,gapp:2,gen:1,get:2,get_entir:[],get_pj_bu:1,get_th_en:2,given:1,glob:2,graph:2,group:1,group_bi:1,hatchet:2,here:[],how:[1,2],ht:2,html:2,indent:[],index:2,input_deploy_dir_str:2,intern:1,ipython:2,javascript:1,jobsiz:1,join:2,json:[],json_formatted_str:[],jupyt:2,launchdat:[1,2],layer:1,let:1,librari:[],like:2,line:[0,1,2],list:2,look:2,lulesh:1,lulesh_gen:2,machin:2,main:2,matplotlib:2,max:1,maxdepth:[],meta:1,metadata:2,might:1,min:1,model:[0,1,2],modul:2,module1:[],more:[],multipl:1,must:1,mydata:[],name:1,nameoflinestoplot:1,need:2,notebook:2,np:2,numpi:2,object:[0,1,2],onli:[0,1],os:2,page:[],panda:2,paramet:[0,1],path:2,pd:2,pj:1,place:2,platform:2,plot:[0,1],plt:2,popul:2,present:1,problem_s:1,profil:1,prolbem_s:[],provid:[1,2],pyplot:2,python:[1,2],re:2,recurs:2,region_cost:[],render:[0,2],renderflamegraph:0,requir:[1,2],run:2,search:[],second:1,see:1,select:[1,2],sent:1,set:1,setgroupbi:1,setvisibleannot:[1,2],setxaggreg:1,setxaxi:[1,2],setyaxi:1,show:[1,2],sl:2,some:2,specifi:1,spot:2,stack:1,stackedlin:2,str:1,subdirectori:2,sum:1,sy:2,te:[],test:[],th_en:[1,2],them:2,thi:[0,1,2],thicket:2,thicketwrapp:2,thing:[0,1],thread:[],toctre:[],tree:[],treescap:1,treescapemodel:[0,2],tt:2,tw:[],two:2,unam:2,us:[1,2],user:2,usr:2,valu:1,variabl:[1,2],visibl:1,visual:0,viz:2,walk:2,want:[0,1],we:2,what:1,which:0,wish:1,x:[1,2],xaxis_nam:1,y:[1,2],yaxis_nam:1,you:[0,1,2],your:1},titles:["StackedLine Viz","TreeScapeModel","Welcome to TreeScape\u2019s documentation!"],titleterms:{build:[],document:2,exampl:2,how:[],indic:2,input:2,object:[],profil:2,s:2,stackedlin:0,tabl:2,th_en:[],tree:2,treescap:2,treescapemodel:1,usag:2,viz:0,welcom:2}})