#%%
import threading
import queue
import time

# Function to be run by each thread
def compute_square(numbers, result_queue):
    for n in numbers:
        time.sleep(1)
        print( str(n) + ", ")
        result_queue.put(n * n)

# Create a queue to store results
result_queue = queue.Queue()

# Define the data for each thread
data1 = [1, 2, 3, 4, 5]
data2 = [6, 7, 8, 9, 10]
data3 = [1, 2, 3, 4, 5]
data4 = [6, 7, 8, 9, 10]

# Create two threads
thread1 = threading.Thread(target=compute_square, args=(data1, result_queue))
thread2 = threading.Thread(target=compute_square, args=(data2, result_queue))
thread3 = threading.Thread(target=compute_square, args=(data3, result_queue))
thread4 = threading.Thread(target=compute_square, args=(data4, result_queue))

# Start the threads
thread1.start()
thread2.start()
thread3.start()
thread4.start()

# Wait for both threads to finish
thread1.join()
thread2.join()
thread3.join()
thread4.join()

# Collect all results
results = []
while not result_queue.empty():
    results.append(result_queue.get())

print("Results:", results)

#%%
