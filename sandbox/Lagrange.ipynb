#%%
import sys
import time

sys.path.append("python")
sys.path.append("viz")

from MyTimer import MyTimer
from StackedLine import StackedLine
from ThicketWrapper import Thicket<PERSON>rapper, TH_ens
from TreeScapeModel import TreeScapeModel

MyTimer("start")
# your script here
th_obj = TH_ens()
th_ens, profiles = th_obj.get_th_ens()


model = TreeScapeModel(th_ens, profiles, "launchdate")
model.setVisibleAnnotations(['main'])
model.setXAxis("launchdate")

#model.setVisibleAnnotations(['CalcLagrangeElements', 'CalcQForElems', 'ApplyMaterialPropertiesForElems'])

sl = StackedLine()
sl.render( model=model, make_stub=1 )

#%%
sl.render( model=model )
#%%
sl.render( model=model )
#%%
sl.renderBoth( model )
#%%
print('hello')
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
sl.renderBoth( model )
#%%
