#%%
# Original data
et = {
    "main": {
        "xaxis": [
            {
                "cali.caliper.version": "2.2.0-dev",
                "cali.channel": "spot",
                "user": "chavez35",
                "launchdate": "1565172972",
                "executablepath": "/g/g90/johnson234/exe/STRIP_HEADER/toss17/impending4.8-3472",
                "libraries": "/etc/fin/etc/home.jafd",
                "cmdline": "[-active COM",
                "cluster": "rockfiro",
                "jobsize": "2",
                "threads": "101",
                "iterations": "11400000",
                "problem_size": "85",
                "num_regions": "11",
                "region_cost": "5",
                "region_balance": "1",
                "elapsed_time": "184.0",
                "figure_of_merit": "6560.0",
                "spot.metrics": "avg#face.duration#inclusive#sum5345"
            },
            {
                "cali.caliper.version": "2.2.0-dev",
                "cali.channel": "spot",
                "jobsize": "2",
                "user": "chavez35",
                "launchdate": "1565172972"
            },
            {
                "cali.caliper.version": "2.2.0-dev",
                "cali.channel": "spot",
                "jobsize": "2",
                "user": "chavez35",
                "launchdate": "1565172972"
            }
            # Add more objects as needed
        ],
        "ydata": [111, 112, 113]
    },
    "TimeIncrement": {
        "xaxis": [
            {
                "cali.caliper.version": "2.2.0-dev",
                "cali.channel": "spot",
                "user": "chavez35",
                "launchdate": "1565172972",
                "executablepath": "/g/g90/johnson234/exe/STRIP_HEADER/toss17/impending4.8-3472",
                "libraries": "/etc/fin/etc/home.jafd",
                "cmdline": "[-active COM",
                "cluster": "rockfiro",
                "jobsize": "2",
                "threads": "101",
                "iterations": "11400000",
                "problem_size": "85",
                "num_regions": "11",
                "region_cost": "5",
                "region_balance": "1",
                "elapsed_time": "184.0",
                "figure_of_merit": "6560.0",
                "spot.metrics": "avg#face.duration#inclusive#sum5345"
            },
            {
                "cali.caliper.version": "2.2.0-dev",
                "cali.channel": "spot",
                "jobsize": "2",
                "user": "chavez35",
                "launchdate": "1565172972"
            },
            {
                "cali.caliper.version": "2.2.0-dev",
                "cali.channel": "spot",
                "jobsize": "2",
                "user": "chavez35",
                "launchdate": "1565172972"
            }
            # Add more objects as needed
        ],
        "ydata": [222, 223, 224]
    }
    # Add more top-level keys as needed
}

# Initialize the transformed data list
tsm_data = []

# Iterate over each top-level key in the original data
for key, value in et.items():
    # Iterate over each item in xaxis
    for i, metadata in enumerate(value["xaxis"]):
        # Find or create the entry for the current index
        if len(tsm_data) <= i:
            tsm_data.append({
                "metadata": {},
                "perftree": {}
            })
        
        # Add or update the metadata and perftree
        tsm_data[i]["metadata"] = metadata
        
        # Ensure the current key is in perftree
        tsm_data[i]["perftree"][key] = value["ydata"][i] if i < len(value["ydata"]) else None


        
import json
ts = json.dumps(tsm_data, indent=4)
print(ts)
# Example usage
print(tsm_data[0]["metadata"]["jobsize"])  # Output: 2
print(tsm_data[0]["perftree"]["main"])     # Output: 111
print(tsm_data[1]["metadata"]["launchdate"])  # Output: 1565172972
print(tsm_data[1]["perftree"]["TimeIncrement"])  # Output: 222

#%%
