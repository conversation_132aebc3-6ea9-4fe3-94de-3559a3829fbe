#%%
from time import time
start = time()

import sys
import platform

machine = platform.uname().machine
sys.path.append("/Users/<USER>/thicket")

import thicket as tt

from glob import glob
import os

PATH = '/Users/<USER>/lulesh_gen/100/'
profiles = [y for x in os.walk(PATH) for y in glob(os.path.join(x[0], '*.cali'))]

#  this contains some metadata we need.
#  also contains the tree data.
th_ens = tt.Thicket.from_caliperreader(profiles)

end = time()
print(f'get_th_ens took {end - start} seconds!')

#%%
