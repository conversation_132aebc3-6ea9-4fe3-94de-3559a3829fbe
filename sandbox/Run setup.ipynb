#%%
import sys
import time

sys.path.append("../python")
sys.path.append("../viz")

from Run import Node, Run

metadata = {
    "caliperreaderversion": 2.3,
    "threads": 23,
    "problem_size": 4,
    "elapsed_time": 7,
    "figure_of_merit": 234
}

child_map = {
    "main": ["lulesh.cycle"],
    "lulesh.cycle": ["LagrangeLeapFrog"],
    "LagrangeLeapFrog": ["LagrangeElements", "LagrangeNodal"]
}


run = Run(metadata, child_map)
run.append_node( Node({"sum":14, "avg":13, "min": 1}, "main") )
run.append_node( Node({"sum":14, "avg":13, "min": 1}, "lulesh.cycle") )
run.append_node( Node({"sum":14, "avg":13, "min": 1}, "LagrangeLeapFrog") )


run2 = Run(metadata2, child_map2)
run2.append_node( Node({"sum":14, "avg":13, "min": 0}, "main") )
run2.append_node( Node({"sum":14, "avg":13, "min": 0}, "lulesh.cycle") )
run2.append_node( Node({"sum":14, "avg":13, "min": 1}, "LagrangeLeapFrog") )


run3 = Run(metadata3, child_map3)
run3.append_node( Node({"sum":14, "avg":13, "min": 0}, "main") )
run3.append_node( Node({"sum":14, "avg":13, "min": 0}, "lulesh.cycle") )
run3.append_node( Node({"sum":14, "avg":13, "min": 1}, "LagrangeLeapFrog") )

run.print()
#%%
