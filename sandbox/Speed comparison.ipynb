#%%
import sys

sys.path.append("python")

from MyTimer import MyTimer
MyTimer("start")

import pandas as pd

# Create a list to store DataFrames
dataframes = []

num = 50000

# Create 10,000 DataFrames with some sample data
for i in range(num):
    data = {'A': [i, i+1, i+2],
            'B': [i, i, i]}
    df = pd.DataFrame(data)
    dataframes.append(df)

MyTimer("Make Dataframe with "+str(num))
    
# Iterate over the list of DataFrames
for index, df in enumerate(dataframes):
    #print(f"DataFrame {index}:")
    #print(df)
    #print()
    x=3

a = MyTimer("Iterate over DataFrame " + str(num))
a.print()
#%%
import sys

sys.path.append("python")

from MyTimer import MyTimer
MyTimer("start")
            
import pandas as pd

# Create a list to store DataFrames
dataframes = []

num = 50000

# Create 10,000 DataFrames with some sample data
for i in range(num):
    data = {'A': [i, i+1, i+2],
            'B': [i, i, i]}
    df = pd.DataFrame(data)
    dataframes.append(df)

MyTimer("Make Dataframe with "+str(num))
    
# Iterate over the list of DataFrames
for index, df in enumerate(dataframes):
    #print(f"DataFrame {index}:")
    #print(df)
    #print()
    x=3

a = MyTimer("Iterate over DataFrame " + str(num))
a.print()
#%%
import sys
import platform
import datetime as dt

sys.path.append("/Users/<USER>/thicket")

import re

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from IPython.display import display
from IPython.display import HTML

import hatchet as ht
import thicket as tt

from glob import glob

import os

PATH = '/Users/<USER>/lulesh_gen/100/'
profiles_glob = [y for x in os.walk(PATH) for y in glob(os.path.join(x[0], '*.cali'))]

th_ens_glob = tt.Thicket.from_caliperreader(profiles_glob)

if th_ens_glob == 0:
    print('no defined')
else:
    print('defined')
#%%
