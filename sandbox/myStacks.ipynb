#%%
import sys

sys.path.append("viz")
from StackedLine import StackedLine

sl = StackedLine()

data = [[16,18,20,14,19,21,5,3], [12, 15, 13, 12, 17, 11,18,16,21], [4, 10, 3, 2, 7, 8,14,17,12]]
sl.render("Line", data)
#%%

#%%

#%%
import sys

sys.path.append("python")
from ThicketWrapper import ThicketWrapper

tw = ThicketWrapper("asdfasdf")
renderDat = tw.get()
#%% raw
data2 = [[16,18,20,14,19,21,5,3], [12, 15, 13, 12, 17, 11,18,16,21], [4, 10, 3, 2, 7, 8,14,17,12]]
sl2 = StackedLine()
sl2.render("Line", data2)
#%%

#%%
