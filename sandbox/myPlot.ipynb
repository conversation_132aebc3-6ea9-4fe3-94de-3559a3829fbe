#%%
import sys
import time

sys.path.append("../python")
sys.path.append("../viz")

from MyTimer import MyTimer
from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
from MultiLine import MultiLine
import json


m = MyTimer("start")

# Test
cali_loc = "/Users/<USER>/cali_data/newdemo/test"
#cali_loc = "/Users/<USER>/lulesh_gen/100/"
caliReader = CaliReader( cali_loc, 15 )

tsm = TreeScapeModel( caliReader )

sl = StackedLine()
sl.setXAxis('launchday')
sl.setYMin(1)
alltests = sorted(tsm, key=lambda x: x.metadata["launchdate"])

grapher = MultiLine(alltests)
grapher.plot_sums( "launchday", "main" )
grapher.plot_sums( "figure_of_merit", "main" )

#%%
grapher.plot_sums( "iterations", "main" )
#%%
import sys
import time
 
sys.path.append("/usr/gapps/spot/treescape/python")
sys.path.append("/usr/gapps/spot/treescape/viz")
 
from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
 
# Test
cali_loc = "/Users/<USER>/newdemo/test"
caliReader = CaliReader( cali_loc, 15 )
 
tsm = TreeScapeModel( caliReader )
 
# Assuming TreeScapeModel behaves like a list of dictionaries
newlist = [run for run in tsm if run.metadata["test"] == "tiny"]
 
sorted_new = sorted( newlist, key=lambda run:run.metadata["launchday"])
tsm.update( sorted_new )
 
sl = StackedLine()
sl.setYMin(0)
sl.setXAxis('launchday') # launchday
 
sl.render(tsm)
#%%
