#%%
import spotdb

dataset_key = args.dataSetKey
last_read = args.lastRead or 0
maxLevels = args.maxLevels or 20

from ErrorHandling import ErrorHandling

ehandle = ErrorHandling()
result = ehandle.check_file( dataset_key )

writeToFile = args.writeToFile or 0
cacheFilename = "cacheToFE.json"
cachePath = dataset_key + '/' + cacheFilename

try:
   f = open( cachePath )
   allJSON = f.read()
   print(allJSON)
   f.close()
   return 1

except IOError:
   a=0  #print("File " + cachePath + " does not exists")

db = spotdb.connect(dataset_key, read_only=True)

runs = []

if last_read > 0:
    runs = db.get_new_runs(last_read)
else:
    runs = db.get_all_run_ids()

# merge "global" and "regionprofile" records into "Runs" structure

globals = db.get_global_data(runs)
records = db.get_regionprofiles(runs)

rundata = { }

for run in runs:
    if run in globals and run in records:
        rundata[run] = { "Data": records[run], "Globals": globals[run] }

output = {
"Runs"          : rundata,
"RunDataMeta"   : db.get_metric_attribute_metadata(),
"RunGlobalMeta" : db.get_global_attribute_metadata()
}


#%%

#%%
