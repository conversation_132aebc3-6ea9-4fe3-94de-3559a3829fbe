#%%
import sys
import time

sys.path.append("python")

from MyTimer import MyTimer

t0 = MyTimer("start")
time.sleep(2)
t1 = MyTimer("howdy")
time.sleep(0.2)
t3 = MyTimer('noxo')
t4 = MyTimer('valinla')
t2 = MyTimer("third asfasdf")
t2 = MyTimer("third asfasdf454")
t2 = MyTimer("third asfasd345345f")

t1.print()
#%%
from time import time

class MyTimer:
    markers = []

    def __init__(self, name):
        if name == "start":
            self.startNow()
        else:
            self.mark(name)

    def startNow(self):
        MyTimer.start = time()
        MyTimer.markers = []

    def mark(self, label_name):
        now = time()
        if not MyTimer.markers:
            last_time = MyTimer.start
        else:
            last_time = MyTimer.markers[-1][1]
        elapsed_time = now - last_time
        MyTimer.markers.append([label_name, elapsed_time])

    def print(self):
        for label, value in MyTimer.markers:
            padded_label = label.ljust(40)
            print(f"{padded_label}        {value:.6f}")

# Example usage:
t = MyTimer("start")
t.mark("first")
t.mark("second")
t.mark("third")
t.print()

#%%

#%%
