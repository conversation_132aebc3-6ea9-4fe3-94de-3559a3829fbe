#%%
import caliperreader as cr
import json

r = cr.CaliperReader()
r.read("/Users/<USER>/newdemo/test/czwVK5Ij-xXgK_msS_0.cali")

glob0 = cr.read_caliper_globals("/Users/<USER>/newdemo/test/czwVK5Ij-xXgK_msS_0.cali")

pretty_json = json.dumps(glob0, indent=4)
print(pretty_json)


type2 = r.attribute("user").get("adiak.type")

print(type2)
#%%
import sys
import time

sys.path.append("../python")
sys.path.append("../viz")

from MyTimer import MyTimer
from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
import json

m = MyTimer("start")

cali_loc = "/Users/<USER>/newdemo/test"
cali_loc = "/Users/<USER>/lulesh_gen/100b/"
caliReader = CaliReader( cali_loc, 15 )

tsm0 = TreeScapeModel( caliReader )
tsm = TreeScapeModel( caliReader )


sl = StackedLine()
sl.setXAxis('jobsize') # launchday

sl.render(tsm)
#%%
