#%%
cali_loc = "/usr/gapps/spot/datasets/newdemo"
filter_meta = "launchday"
xaxis = 'launchday'

import sys
import time
 
sys.path.append("/usr/gapps/spot/treescape/python")
sys.path.append("/usr/gapps/spot/treescape/viz")
 
from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
 
caliReader = CaliReader( cali_loc, 15 )
 
tsm = TreeScapeModel( caliReader )
 
# Assuming TreeScapeModel behaves like a list of dictionaries
newlist = [run for run in tsm if run.metadata["test"] == "tiny"]
 
sorted_new = sorted( newlist, key=lambda run:run.metadata[filter_meta])
tsm.update( sorted_new )
 
sl = StackedLine()
sl.setYMin(0)
sl.setXAxis(xaxis) # launchday
 
sl.render(tsm)
#%%
