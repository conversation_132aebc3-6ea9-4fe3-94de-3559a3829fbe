#%%
import sys
import time

sys.path.append("../python")
sys.path.append("../viz")

from MyTimer import MyTimer
from StackedLine import StackedLine
from ThicketWrapper import Thicket<PERSON>rapper, TH_ens
from TreeScapeModel import TreeScapeModel

MyTimer("start")
# your script here
th_obj = TH_ens()
th_ens, profiles = th_obj.get_th_ens()


mod = TreeScapeModel(th_ens, profiles, "launchdate")
mod.setVisibleAnnotations(['main'])
mod.setXAxis("launchdate")

#model.setVisibleAnnotations(['CalcLagrangeElements', 'CalcQForElems', 'ApplyMaterialPropertiesForElems'])

sl = StackedLine()
sl.render( model=mod, xaxis="launchdate", annotations=['LagrangeLeapFrog'] )
#%%
sl.renderBoth( model )

#%%
sl.renderBoth( model )
#%%
