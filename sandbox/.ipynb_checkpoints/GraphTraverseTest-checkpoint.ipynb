#%%
import sys

sys.path.append("../python")
from GraphTraverseModel import GraphTraverseModel

gtm = GraphTraverseModel()
gtm.getChildrenNamesFor("LagrangeElements")
#%%
gtm.getChildrenNamesFor("LagrangeLeapFrog")
#%%
sys.path.append("python")
from GraphTraverseModel import GraphTraverseModel

gtm = GraphTraverseModel()
map0 = gtm.getChildToParentMapping()
print(map0)
#%%
import sys

sys.path.append("python")
sys.path.append("viz")

from StackedLine import StackedLine
from ThicketWrapper import Thicket<PERSON>rapper, TH_ens
from TreeScapeModel import TreeScapeModel

# your script here
th_obj = TH_ens()

th_ens, profiles = th_obj.get_th_ens()


model = TreeScapeModel(th_ens, profiles, "launchdate")

#%%
