#%%
import sys
import time

sys.path.append("/usr/gapps/spot/treescape/python")
sys.path.append("/usr/gapps/spot/treescape/viz")

from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
import json

cali_loc = "/usr/gapps/spot/datasets/newdemo/test"
caliReader = CaliReader( cali_loc, 15 )

tsm = TreeScapeModel( caliReader )

# Assuming TreeScapeModel behaves like a list of dictionaries
newlist = [run for run in tsm if run.metadata["test"] == "tiny"]

sorted_new = sorted( newlist, key=lambda run:run.metadata["launchday"])
tsm.update( sorted_new )

sl = StackedLine()
sl.setXAxis('launchday') # launchday

sl.render(tsm)
#%%
caliReader = CaliReader("/usr/gapps/spot/datasets/newdemo/test", 15)
tsm = TreeScapeModel(caliReader)
sl = StackedLine()
sl.setXAxis('launchday')
sl.setYMin(1)
alltests = sorted(tsm, key=lambda x: x.metadata["launchdate"])
#%%
for testname in { t.metadata["test"] for t in alltests }:
    print(testname)
    sl.render([t for t in alltests if t.metadata["test"] == testname])
#%%
