#%%
import sys
import time

sys.path.append("/g/g0/pascal/stacked/python")
sys.path.append("/g/g0/pascal/stacked/viz")

from MyTimer import MyTimer
from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
import json


m = MyTimer("start")

# Test
cali_loc = "/Users/<USER>/newdemo/test"
#cali_loc = "/Users/<USER>/lulesh_gen/100b/"
cali_loc = "/usr/gapps/spot/datasets/newdemo/test"
caliReader = CaliReader( cali_loc, 15 )

tsm = TreeScapeModel( caliReader )

# Assuming TreeScapeModel behaves like a list of dictionaries
newlist = [run for run in tsm if run.metadata["test"] == "tiny"]

sorted_new = sorted( newlist, key=lambda run:run.metadata["launchday"])
tsm.update( sorted_new )

sl = StackedLine()
sl.setXAxis('launchday') # launchday

sl.render(tsm)
#%%
caliReader = CaliReader("/usr/gapps/spot/datasets/newdemo/test", 15)
tsm = TreeScapeModel(caliReader)
sl = StackedLine()
sl.setXAxis('launchday')
sl.setYMin(1)
alltests = sorted(tsm, key=lambda x: x.metadata["launchdate"])
#%%
for testname in { t.metadata["test"] for t in alltests }:
    print(testname)
    sl.render([t for t in alltests if t.metadata["test"] == testname])
#%%
