#%%
dataset = "/usr/gapps/spot/datasets/newdemo/test"
xaxis = "launchday"
metadata_key = "test"
processes_for_parallel_read = 15

import sys
 
sys.path.append("../python")
sys.path.append("../viz")
 
from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
 
caliReader = CaliReader(dataset, processes_for_parallel_read)
tsm = TreeScapeModel(caliReader)
 
sorted_tsm = sorted(tsm, key=lambda run:run.metadata[xaxis])
tsm.update(sorted_tsm)
 
sl = StackedLine()
sl.setYMin(0)
sl.setXAxis(xaxis)
#%%
for testname in { t.metadata[metadata_key] for t in tsm }:
    print(testname)
    sl.render([t for t in tsm if t.metadata[metadata_key] == testname])
#%%

#%%
