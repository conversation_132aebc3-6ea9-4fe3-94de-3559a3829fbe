#%%
import sys
import time

sys.path.append("python")
sys.path.append("viz")

from MyTimer import MyTimer
from StackedLine import <PERSON>acked<PERSON>ine
from ThicketWrapper import Thicket<PERSON>rapper, TH_ens
from TreeScapeModel import TreeScapeModel

MyTimer("start")
# your script here
th_obj = TH_ens()
th_ens, profiles = th_obj.get_th_ens()


mod = TreeScapeModel(th_ens, profiles, "launchdate")

sl = StackedLine()
sl.render( model=mod, 
          annotations=['CalcLagrangeElements', 'CalcQForElems', 'ApplyMaterialPropertiesForElems'], 
          make_stub=0 )
#%%
sl.render( model=mod, annotations=['main'], make_stub=1 )
#%%
def combine_objects(array):
    combined_data = {}

    for obj in array:
        for key, data in obj.items():
            xaxis = data['xaxis'][0]
            ydata = data['ydata'][0]

            if xaxis not in combined_data:
                combined_data[xaxis] = {}

            if key not in combined_data[xaxis]:
                combined_data[xaxis][key] = {
                    'name': data['name'],
                    'xaxis': [xaxis],
                    'ydata': [0.0]
                }

            combined_data[xaxis][key]['ydata'][0] += ydata

    # Convert combined_data back into the desired format
    result = []
    for xaxis, data in combined_data.items():
        result.append(data)

    return result

# Example usage:
array = [
    {
        'main': {
            'name': 'main',
            'xaxis': ['1565473288'],
            'ydata': [79.950869]
        },
        'lulesh.cycle': {
            'name': 'lulesh.cycle',
            'xaxis': ['1565473288'],
            'ydata': [79.885921]
        },
        'TimeIncrement': {
            'name': 'TimeIncrement',
            'xaxis': ['6'],
            'ydata': [0.032186]
        }
    },
    {
        'main': {
            'name': 'main',
            'xaxis': ['1565473288'],
            'ydata': [20.049131]
        },
        'lulesh.cycle': {
            'name': 'lulesh.cycle',
            'xaxis': ['1565473288'],
            'ydata': [20.114079]
        },
        'TimeIncrement': {
            'name': 'TimeIncrement',
            'xaxis': ['1565473288'],
            'ydata': [0.067814]
        }
    }
]

combined_result = combine_objects(array)
print(combined_result)

#%%
