#%%
import time

def my_func():
    a = [i for i in range(1000000)]
    time.sleep(1)  # Simulate some computation
    b = [i * 2 for i in a]
    del a  # Simulate freeing up memory
    
    
    
import sys

# Get size of variables before function call
start_memory = sys.getsizeof({})

# Call the function
my_func()

# Get size of variables after function call
end_memory = sys.getsizeof({})

# Calculate memory usage
memory_usage = end_memory - start_memory
print("Memory usage:", memory_usage, "bytes")

#%%
