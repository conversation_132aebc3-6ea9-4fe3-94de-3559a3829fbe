#%%
import time

def my_func():
    a = [i for i in range(1000000)]
    time.sleep(1)  # Simulate some computation
    b = [i * 2 for i in a]
    del a  # Simulate freeing up memory
    
    
    
import sys

# Get size of variables before function call
start_memory = sys.getsizeof({})

# Call the function
my_func()
a = [23842834234,234,842834234,234,842834234,234,842834234,234,842834234,234,842834234,234,842834234,234,842834234,234]

# Get size of variables after function call
end_memory = sys.getsizeof({})

# Calculate memory usage
memory_usage = end_memory - start_memory
print("Memory usage:", memory_usage, "bytes")
print(end_memory)
#%%
import time
import sys

def my_func():
    a = [i for i in range(1000000)]
    start_memory = sys.getsizeof(a)  # Get size of list 'a'
    time.sleep(1)  # Simulate some computation
    b = [i * 2 for i in a]
    del a  # Simulate freeing up memory
    end_memory = sys.getsizeof(b)  # Get size of list 'b'
    memory_usage = end_memory - start_memory
    return memory_usage

memory_increase = my_func()
print("Memory increase:", memory_increase, "bytes")

#%%
import time
import sys

def my_func():
    a = [i for i in range(10000000)]  # Create a larger list
    start_memory = sys.getsizeof(a)  # Get size of list 'a'
    time.sleep(1)  # Simulate some computation
    b = [i * 2 for i in a]
    del a  # Simulate freeing up memory
    end_memory = sys.getsizeof(b)  # Get size of list 'b'
    memory_usage = end_memory - start_memory
    return memory_usage

memory_increase = my_func()
print("Memory increase:", memory_increase, "bytes")

#%%
%load_ext memory_profiler

import random
import time

# Define a function to profile
def my_func():
    numbers = []
    for _ in range(100000):
        numbers.append(random.randint(1, 1000))
    time.sleep(2)
    return numbers

# Profile the function using %memit
%memit my_func()

#%%
