#%%
import sys
import time

sys.path.append("/Users/<USER>/treescape-venv3/lib/python3.9/site-packages")
#sys.path.append("/usr/gapps/spot/treescape")
sys.path.append("/Users/<USER>/treescape")

import treescape as tr

# Test
cali_loc = "/usr/gapps/spot/datasets/newdemo/test"
cali_loc = "/Users/<USER>/cali_data/newdemo/small_test"
#cali_loc = "/Users/<USER>/cali_data/lulesh_gen/100/"
caliReader = tr.CaliReader( cali_loc, 15 )

tsm = tr.TreeScapeModel( caliReader )

sl = tr.StackedLine()
sl.setXAxis('launchday')
sl.setYMin(1)
alltests = sorted(tsm, key=lambda x: x.metadata["launchday"])

print(alltests)

grapher = tr.MultiLine(alltests)
grapher.plot_sums( "launchday", "main" )
grapher.plot_sums( "figure_of_merit", "main" )

sl.render(tsm)
#print(sl.get_PJ_bus('launchday'))

#%%
grapher.plot_sums( "iterations", "main" )
#%%
import sys
import time
 
sys.path.append("/usr/gapps/spot/treescape/python")
sys.path.append("/usr/gapps/spot/treescape/viz")
 
from StackedLine import StackedLine
from TreeScapeModel import TreeScapeModel
from CaliReader import CaliReader
 
# Test
cali_loc = "/Users/<USER>/newdemo/test"
caliReader = CaliReader( cali_loc, 15 )
 
tsm = TreeScapeModel( caliReader )
 
# Assuming TreeScapeModel behaves like a list of dictionaries
newlist = [run for run in tsm if run.metadata["test"] == "tiny"]
 
sorted_new = sorted( newlist, key=lambda run:run.metadata["launchday"])
tsm.update( sorted_new )
 
sl = StackedLine()
sl.setYMin(0)
sl.setXAxis('launchday') # launchday
 
sl.render(tsm)
#%%
tsm3 = TreeScapeModel( caliReader )

alltests = sorted(tsm3, key=lambda x: x.metadata["launchday"])

grapher = MultiLine(alltests)
grapher.plot_sums( "launchday", "main" )
grapher.plot_sums( "figure_of_merit", "main" )


#%%
import matplotlib.pyplot as plt
import numpy as np

# Generate example data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create the plot
plt.figure(figsize=(6, 4))
plt.plot(x, y, label="Sine Wave", color="blue")
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Sine Wave Plot")
plt.legend()

# Save as SVG
plt.savefig("plot.svg", format="svg")

# Show the plot
plt.show()

#%%
