#%%
import sys

sys.path.append("../python")
from GraphTraverseModel import GraphTraverseModel

gtm = GraphTraverseModel()
gtm.getChildrenNamesFor("LagrangeElements")
#%%
gtm.getChildrenNamesFor("LagrangeLeapFrog")
#%%
sys.path.append("python")
from GraphTraverseModel import GraphTraverseModel

gtm = GraphTraverseModel()
map0 = gtm.getChildToParentMapping()
print(map0)
#%%
import sys

sys.path.append("../python")
sys.path.append("../viz")

from StackedLine import StackedLine
from ThicketWrapper import ThicketWrapper, TH_ens
from TreeScapeModel import TreeScapeModel

# your script here
th_obj = TH_ens()

th_ens, profiles = th_obj.get_th_ens()

gtm = GraphTraverseModel(self.th_ens, self.profiles)
# childrenMap = {'main': ['lulesh.cycle'], 'lulesh.cycle': ['TimeIncrement', 'LagrangeLeapFrog'], 'LagrangeLeapFrog': ['LagrangeNodal', 'LagrangeElements', 'CalcTimeConstraintsForElems'], 'CalcTimeConstraintsForElems': [], 'LagrangeElements': ['CalcLagrangeElements', 'CalcQForElems', 'ApplyMaterialPropertiesForElems'], 'ApplyMaterialPropertiesForElems': ['EvalEOSForElems'], 'EvalEOSForElems': ['CalcEnergyForElems'], 'CalcEnergyForElems': [], 'CalcLagrangeElements': ['CalcKinematicsForElems'], 'CalcKinematicsForElems': [], 'CalcQForElems': ['CalcMonotonicQForElems'], 'CalcMonotonicQForElems': [], 'LagrangeNodal': ['CalcForceForNodes'], 'CalcForceForNodes': ['CalcVolumeForceForElems'], 'CalcVolumeForceForElems': ['IntegrateStressForElems', 'CalcHourglassControlForElems'], 'CalcHourglassControlForElems': ['CalcFBHourglassForceForElems'], 'CalcFBHourglassForceForElems': [], 'IntegrateStressForElems': [], 'TimeIncrement': []}
childrenMap = gtm.getParentToChildMapping()

#%%
