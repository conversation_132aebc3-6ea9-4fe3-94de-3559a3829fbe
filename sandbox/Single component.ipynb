#%%
import sys
import time

sys.path.append("../python")
sys.path.append("../viz")

from MyTimer import MyTimer
from StackedLine import StackedLine
from ThicketReader import ThicketReader, TH_ens
from TreeScapeModel import TreeScapeModel

MyTimer("start")
# your script here
th_obj = TH_ens()
th_ens, profiles = th_obj.get_th_ens()
thicketReader = ThicketReader(th_ens, profiles, "launchdate")


mod = TreeScapeModel(thicketReader)
mod.setDrillLevel(['main'])


sl = StackedLine()
sl.render( model=mod, drill_level=['LagrangeLeapFrog'] )
#%%
gtm = GraphTraverseModel(self.th_ens, self.profiles)
# childrenMap = {'main': ['lulesh.cycle'], 'lulesh.cycle': ['TimeIncrement', 'LagrangeLeapFrog'], 'LagrangeLeapFrog': ['LagrangeNodal', 'LagrangeElements', 'CalcTimeConstraintsForElems'], 'CalcTimeConstraintsForElems': [], 'LagrangeElements': ['CalcLagrangeElements', 'CalcQForElems', 'ApplyMaterialPropertiesForElems'], 'ApplyMaterialPropertiesForElems': ['EvalEOSForElems'], 'EvalEOSForElems': ['CalcEnergyForElems'], 'CalcEnergyForElems': [], 'CalcLagrangeElements': ['CalcKinematicsForElems'], 'CalcKinematicsForElems': [], 'CalcQForElems': ['CalcMonotonicQForElems'], 'CalcMonotonicQForElems': [], 'LagrangeNodal': ['CalcForceForNodes'], 'CalcForceForNodes': ['CalcVolumeForceForElems'], 'CalcVolumeForceForElems': ['IntegrateStressForElems', 'CalcHourglassControlForElems'], 'CalcHourglassControlForElems': ['CalcFBHourglassForceForElems'], 'CalcFBHourglassForceForElems': [], 'IntegrateStressForElems': [], 'TimeIncrement': []}
childrenMap = gtm.getParentToChildMapping()

#%%

#%%
sl.render( model=mod, components = ['flamegraph'] )
#%%
thicketReader = ThicketReader(th_ens, profiles, "iterations")

mod2 = TreeScapeModel(thicketReader)
mod2.setDrillLevel(['CalcLagrangeElements', 'CalcQForElems', 'ApplyMaterialPropertiesForElems'])
mod2.setXAggregation("sum")
mod2.setYMax(110)

sl = StackedLine()
sl.render( model=mod2 )
#%%
thicketReader = ThicketReader(th_ens, profiles, "iterations")

mod2 = TreeScapeModel(thicketReader)
mod2.setDrillLevel(['main'])
mod2.setXAggregation("sum")
mod2.setYMax(800)
mod2.setYMin(200)

sl = StackedLine()
sl.render( model=mod2 )
#%%

#%%
