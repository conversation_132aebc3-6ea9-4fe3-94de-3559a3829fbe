#%%
dataset = "/usr/gapps/spot/datasets/lulesh_gen/32000"
xaxis = "launchdate"
metadata_key = "user"
processes_for_parallel_read = 15

import sys
 
sys.path.append("/usr/gapps/spot/treescape-ven/lib/python3.9/site-packages")
sys.path.append("/usr/gapps/spot/treescape")

import treescape as tr
 
caliReader = tr.CaliReader(dataset, processes_for_parallel_read)
tsm = tr.TreeScapeModel(caliReader)
 
sorted_tsm = sorted(tsm, key=lambda run:run.metadata[xaxis])
tsm.update(sorted_tsm)
 
sl = tr.StackedLine()
sl.setYMin(0)
sl.setXAxis(xaxis)
sl.setHeight(350)

sl.setXAggregation(tr.StackedLine.MIN)
sl.setDrillLevel(["TimeIncrement", "LagrangeLeapFrog"])
#sl.setYMax(3500)
sl.setYMin(0)
sl.setWidth(700)

sl.render(tsm)
#%%
#print(tsm[0])
for testname in { t.metadata[metadata_key] for t in tsm }:
    print(testname)
    sl.render([t for t in tsm if t.metadata[metadata_key] == testname])
#%%
