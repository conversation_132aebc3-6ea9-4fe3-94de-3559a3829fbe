#%%
##
##This is a demo that uses TreeScape to track performance changes in nightly test runs.
##
##For a sample lulesh-based dataset that's currently on LC, this demo shows: 
## - A summary graph of the performance changes of 'main' for all tests over time, which is intended to reveal performance shifts at a quick glance
## - For each test, a detailed stacked line graph that you can click to dig into the performance of individual code regions.
## - For each day in the stacked line graph for a test, you can expand either the metadata or a flamegraph showing the performance/metadata of that specific day.
##
## The relevant code objects for controlling this are documented below. As of this writing, this is intended as a friendly beta-test. We are interested in feedback 
## and bug reports.
##

cali_file_loc = "/usr/gapps/spot/datasets/newdemo/test"
xaxis = "launchday"
metadata_key = "test"
processes_for_parallel_read = 15
initial_regions = ["main"]

import sys

sys.path.append("/usr/gapps/spot/treescape-ven/lib/python3.9/site-packages")
sys.path.append("/usr/gapps/spot/treescape")

import treescape as tr
#%%
##
##You load your caliper files into a TreeScapeModel, which is essentially a python list of runs. Each list entry is one run. You can
##access that run's key/value metadata with the run.metadata dict. 
##By sorting/filtering/aggregating that python list you can control what data the subsequent visualizations operate on.
##This demo will use the metadata kay 'launchday' to sort dataset. It will then use the metadata key 'test' (which represents the test name
##associated with that run) to filter the data into different graphs. That will show performance over time per test.
##
caliReader = tr.CaliReader( cali_file_loc, processes_for_parallel_read )
tsm = tr.TreeScapeModel( caliReader)
#Always be sure to sort your data into some reasonable way. 
alltests = sorted(tsm, key=lambda x: x.metadata[xaxis])
#%%
##
##For each initial_region (currently just 'main'), draw a graph showing all test's performance for that region over time.
##MultiLine is a relatively-simple visualization component that draws multiple lines on a graph. It takes the xaxis metadata key, 
##the region to draw, and a metadata key to use for creating graphs.
##
grapher = tr.MultiLine(alltests)
for region in initial_regions:
    grapher.plot_sums( xaxis, region, metadata_key )
#%%
##
##StackedLine is a visualization component that creates interactive stackedline charts. These can be cliked through to zoom in on regions.
##When mouse-hovering over a specific day it can show the flamegraph/metadata for that day (if you expand that region).
##
sl = tr.StackedLine()

##
## Set parameters (size, what region to start at, aggregation) that control how we'll draw graphs.
##
# sets the X axis variable in the plot
sl.setXAxis(xaxis)
# If aggregate values.  possible values are tr.StackedLine.AVG, tr.StackedLine.MIN, tr.StackedLine.MAX, tr.StackedLine.SUM 
sl.setXAggregation(tr.StackedLine.AVG)
# sets initial nodes to plot.  can plot nodes from the same or different levels of drill down.  You may click on the nodes to drill down.
sl.setDrillLevel( initial_regions )
# set the highest value in the plot
#sl.setYMax(3500)
# set the lowest value in the plot
#sl.setYMin(200)
# set the pixel width of the plot
sl.setWidth(1100)
# set the pixel height of the plot
sl.setHeight(250)

##
## For each test, filter the TreeScapeModel list to be data for that test. Then draw a stacked line graph for that
## filtered set of data. 
##
for testname in { t.metadata[metadata_key] for t in tsm }:
    print(testname)
    # render each test.  click on "Run Info" button to see flamegraph and metadata
    sl.render([t for t in tsm if t.metadata[metadata_key] == testname])
#%%
