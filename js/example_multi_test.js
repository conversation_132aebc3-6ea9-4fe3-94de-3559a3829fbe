{
    'metadata'
:
    {
        'cali.caliper.version'
    :
        '2.5.0', 'spot.metrics'
    :
        'min#inclusive#sum#time.duration,max#inclusive#sum#time.duration,avg#inclusive#sum#time.duration,sum#inclusive#sum#time.duration', 'spot.timeseries.metrics'
    :
        '', 'spot.format.version'
    :
        '2', 'spot.options'
    :
        '', 'cali.channel'
    :
        'spot', 'user'
    :
        '<PERSON>', 'launchdate'
    :
        '1612195181', 'executablepath'
    :
        '/usr/WS1/legendre/spot/spack/opt/spack/linux-rhel7-broadwell/clang-11.0.0/lulesh-2.0.3-v1-o24v6pfo2lmmam42hb275vkvuhkz5tdm/bin/lulesh2.0', 'libraries'
    :
        '[linux-vdso.so.1,/g/g0/legendre/workspace/spot/demo/libsettime.so,/usr/WS1/legendre/spot/spack/opt/spack/linux-rhel7-broadwell/clang-11.0.0/caliper-2.5.0-jres7njyqd6mlx3lvdty6nlv4wti6mo7/lib64/libcaliper.so.2,/usr/WS1/legendre/spot/spack/opt/spack/linux-rhel7-broadwell/clang-11.0.0/adiak-0.2.1-vfj4uzh23onwiuq2zrnwf76xav63wueq/lib/libadiak.so,/lib64/libm.so.6,/g/g0/legendre/workspace/spot/demo/clang11.0.0/libmpicxx.so.12,/g/g0/legendre/workspace/spot/demo/clang11.0.0/libmpi.so.12,/lib64/libstdc++.so.6,/lib64/libomp.so,/lib64/libgcc_s.so.1,/lib64/libpthread.so.0,/lib64/libc.so.6,/g/g0/legendre/workspace/spot/demo/clang11.0.0/libpapi.so.*******,/lib64/libdw.so.1,/usr/WS1/legendre/spot/spack/opt/spack/linux-rhel7-broadwell/clang-11.0.0/libunwind-1.5.0-usn5fqakrhq6v7dche75qolrbfm2q6v6/lib/libunwind.so.8,/lib64/libdl.so.2,/lib64/librt.so.1,/lib64/ld-linux-x86-64.so.2,/lib64/libnuma.so.1,/lib64/libibverbs.so.1,/lib64/libpsm2.so.2,/usr/WS1/legendre/spot/spack/opt/spack/linux-rhel8-broadwell/gcc-8.3.1/flux-core-0.23.0-kziybk2kkcnpb273ulyahvnpw72vva42/lib/flux/libpmi2.so.0,/g/g0/legendre/workspace/spot/demo/clang11.0.0/libgfortran.so.3,/g/g0/legendre/workspace/spot/demo/clang11.0.0/libquadmath.so.0,/lib64/libpfm.so.4,/lib64/libsensors.so.4,/lib64/libelf.so.1,/lib64/libz.so.1,/lib64/liblzma.so.5,/lib64/libbz2.so.1,/lib64/libnl-route-3.so.200,/lib64/libnl-3.so.200,/lib64/libdebuginfod-0.180.so,/lib64/libcurl.so.4,/lib64/libnghttp2.so.14,/lib64/libidn2.so.0,/lib64/libssh.so.4,/lib64/libpsl.so.5,/lib64/libssl.so.1.1,/lib64/libcrypto.so.1.1,/lib64/libgssapi_krb5.so.2,/lib64/libkrb5.so.3,/lib64/libk5crypto.so.3,/lib64/libcom_err.so.2,/lib64/libldap-2.4.so.2,/lib64/liblber-2.4.so.2,/lib64/libbrotlidec.so.1,/lib64/libunistring.so.2,/lib64/libkrb5support.so.0,/lib64/libkeyutils.so.1,/lib64/libresolv.so.2,/lib64/libsasl2.so.3,/lib64/libbrotlicommon.so.1,/lib64/libselinux.so.1,/lib64/libcrypt.so.1,/lib64/libpcre2-8.so.0,/lib64/libarcher.so,/usr/lib64/libibverbs/libmlx5-rdmav25.so,/usr/lib64/libibverbs/libmlx4-rdmav25.so,/usr/lib64/libibverbs/libhns-rdmav25.so,/usr/lib64/libibverbs/libbnxt_re-rdmav25.so,/usr/lib64/libibverbs/librxe-rdmav25.so,/usr/lib64/libibverbs/libcxgb4-rdmav25.so,/usr/lib64/libibverbs/libvmw_pvrdma-rdmav25.so,/usr/lib64/libibverbs/libqedr-rdmav25.so,/usr/lib64/libibverbs/libhfi1verbs-rdmav25.so,/usr/lib64/libibverbs/libi40iw-rdmav25.so,/usr/lib64/libibverbs/libsiw-rdmav25.so,/lib64/libnss_files.so.2]', 'cluster'
    :
        'opal', 'jobsize'
    :
        '27', 'launchday'
    :
        '1612137600', 'numhosts'
    :
        '1', 'test'
    :
        'tiny', 'mpi'
    :
        'mvapich2', 'compiler'
    :
        'clang', 'compilerversion'
    :
        '"11.0.0 "', 'threads'
    :
        '1', 'iterations'
    :
        '100', 'problem_size'
    :
        '30', 'num_regions'
    :
        '11', 'region_cost'
    :
        '1', 'region_balance'
    :
        '1', 'version'
    :
        '2.0.3-v1', 'elapsed_time'
    :
        '6.659052', 'figure_of_merit'
    :
        '10947.504020'
    }
,
    'perftree'
:
    {
        'main'
    :
        {
            'min'
        :
            '6.665552', 'max'
        :
            '6.680731', 'avg'
        :
            '6.671876', 'sum'
        :
            '6.671876'
        }
    ,
        'lulesh.cycle'
    :
        {
            'min'
        :
            '6.643841', 'max'
        :
            '6.659011', 'avg'
        :
            '6.650186', 'sum'
        :
            '6.650186'
        }
    ,
        'TimeIncrement'
    :
        {
            'min'
        :
            '0.331444', 'max'
        :
            '1.731792', 'avg'
        :
            '1.048736', 'sum'
        :
            '1.048736'
        }
    ,
        'LagrangeLeapFrog'
    :
        {
            'min'
        :
            '4.918036', 'max'
        :
            '6.324476', 'avg'
        :
            '5.600978', 'sum'
        :
            '5.600978'
        }
    ,
        'LagrangeNodal'
    :
        {
            'min'
        :
            '3.572421', 'max'
        :
            '4.122038', 'avg'
        :
            '3.712621', 'sum'
        :
            '3.712621'
        }
    ,
        'CalcForceForNodes'
    :
        {
            'min'
        :
            '2.914928', 'max'
        :
            '3.456442', 'avg'
        :
            '3.356065', 'sum'
        :
            '3.356065'
        }
    ,
        'CalcVolumeForceForElems'
    :
        {
            'min'
        :
            '2.141991', 'max'
        :
            '3.335759', 'avg'
        :
            '2.708577', 'sum'
        :
            '2.708577'
        }
    ,
        'IntegrateStressForElems'
    :
        {
            'min'
        :
            '0.364922', 'max'
        :
            '0.518198', 'avg'
        :
            '0.418994', 'sum'
        :
            '0.418994'
        }
    ,
        'CalcHourglassControlForElems'
    :
        {
            'min'
        :
            '1.759146', 'max'
        :
            '2.784262', 'avg'
        :
            '2.258370', 'sum'
        :
            '2.258370'
        }
    ,
        'CalcFBHourglassForceForElems'
    :
        {
            'min'
        :
            '0.618606', 'max'
        :
            '1.044456', 'avg'
        :
            '0.764389', 'sum'
        :
            '0.764389'
        }
    ,
        'LagrangeElements'
    :
        {
            'min'
        :
            '1.311311', 'max'
        :
            '2.648913', 'avg'
        :
            '1.865438', 'sum'
        :
            '1.865438'
        }
    ,
        'CalcLagrangeElements'
    :
        {
            'min'
        :
            '0.392617', 'max'
        :
            '0.587948', 'avg'
        :
            '0.460942', 'sum'
        :
            '0.460942'
        }
    ,
        'CalcKinematicsForElems'
    :
        {
            'min'
        :
            '0.376897', 'max'
        :
            '0.570647', 'avg'
        :
            '0.444783', 'sum'
        :
            '0.444783'
        }
    ,
        'CalcQForElems'
    :
        {
            'min'
        :
            '0.550863', 'max'
        :
            '1.260494', 'avg'
        :
            '0.823579', 'sum'
        :
            '0.823579'
        }
    ,
        'CalcMonotonicQForElems'
    :
        {
            'min'
        :
            '0.093697', 'max'
        :
            '0.155734', 'avg'
        :
            '0.117719', 'sum'
        :
            '0.117719'
        }
    ,
        'ApplyMaterialPropertiesForElems'
    :
        {
            'min'
        :
            '0.299890', 'max'
        :
            '1.349518', 'avg'
        :
            '0.576855', 'sum'
        :
            '0.576855'
        }
    ,
        'EvalEOSForElems'
    :
        {
            'min'
        :
            '0.286480', 'max'
        :
            '1.333763', 'avg'
        :
            '0.562631', 'sum'
        :
            '0.562631'
        }
    ,
        'CalcEnergyForElems'
    :
        {
            'min'
        :
            '0.181275', 'max'
        :
            '0.987393', 'avg'
        :
            '0.394895', 'sum'
        :
            '0.394895'
        }
    ,
        'CalcTimeConstraintsForElems'
    :
        {
            'min'
        :
            '0.019197', 'max'
        :
            '0.026601', 'avg'
        :
            '0.022347', 'sum'
        :
            '0.022347'
        }
    }
}